<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { goldApi } from '@/api/gold'

interface ChartDataPoint {
  time: string
  open: number
  high: number
  low: number
  close: number
  volume?: number
}

interface MarketData {
  type: string
  currentPrice: number
  change: number
  changePercent: number
  high: number
  low: number
  open: number
}

interface Props {
  chartData: ChartDataPoint[]
  marketData: MarketData[]
}

const props = defineProps<Props>()

const loading = ref(false)
const analysisData = ref<any>(null)

// 技术指标分析
const technicalAnalysis = computed(() => {
  if (!props.chartData || props.chartData.length < 20) {
    return {
      ma5: '--',
      ma10: '--',
      ma20: '--',
      rsi: '--',
      trend: 'neutral',
    }
  }

  const prices = props.chartData.map(item => item.close)
  const ma5 = goldApi.calculateMA(prices, 5)
  const ma10 = goldApi.calculateMA(prices, 10)
  const ma20 = goldApi.calculateMA(prices, 20)
  const rsi = goldApi.calculateRSI(prices, 14)

  const latestMA5 = ma5[ma5.length - 1]
  const latestMA10 = ma10[ma10.length - 1]
  const latestMA20 = ma20[ma20.length - 1]
  const latestRSI = rsi[rsi.length - 1]

  // 判断趋势
  let trend = 'neutral'
  if (latestMA5 > latestMA10 && latestMA10 > latestMA20) {
    trend = 'up'
  }
  else if (latestMA5 < latestMA10 && latestMA10 < latestMA20) {
    trend = 'down'
  }

  return {
    ma5: Number.isNaN(latestMA5) ? '--' : latestMA5.toFixed(2),
    ma10: Number.isNaN(latestMA10) ? '--' : latestMA10.toFixed(2),
    ma20: Number.isNaN(latestMA20) ? '--' : latestMA20.toFixed(2),
    rsi: Number.isNaN(latestRSI) ? '--' : latestRSI.toFixed(2),
    trend,
  }
})

// 市场情绪分析
const marketSentiment = computed(() => {
  if (!props.marketData || props.marketData.length === 0) {
    return {
      bullish: 0,
      bearish: 0,
      neutral: 0,
      overall: 'neutral',
    }
  }

  let bullish = 0
  let bearish = 0
  let neutral = 0

  props.marketData.forEach((market) => {
    if (market.change > 0)
      bullish++
    else if (market.change < 0)
      bearish++
    else neutral++
  })

  const total = props.marketData.length
  const bullishPercent = (bullish / total) * 100
  const bearishPercent = (bearish / total) * 100

  let overall = 'neutral'
  if (bullishPercent > 60)
    overall = 'up'
  else if (bearishPercent > 60)
    overall = 'down'

  return {
    bullish: bullishPercent,
    bearish: bearishPercent,
    neutral: (neutral / total) * 100,
    overall,
  }
})

// 生成AI分析文本
function generateAnalysisText() {
  const tech = technicalAnalysis.value
  const sentiment = marketSentiment.value

  let analysis = ''

  // 技术面分析
  if (tech.trend === 'up') {
    analysis += '技术面显示多头排列，短期均线上穿长期均线，呈现上涨趋势。'
  }
  else if (tech.trend === 'down') {
    analysis += '技术面显示空头排列，短期均线下穿长期均线，呈现下跌趋势。'
  }
  else {
    analysis += '技术面显示震荡整理，各均线交织，方向不明确。'
  }

  // RSI分析
  const rsiValue = Number.parseFloat(tech.rsi)
  if (!Number.isNaN(rsiValue)) {
    if (rsiValue > 70) {
      analysis += 'RSI指标显示超买状态，需警惕回调风险。'
    }
    else if (rsiValue < 30) {
      analysis += 'RSI指标显示超卖状态，可能存在反弹机会。'
    }
    else {
      analysis += 'RSI指标处于正常区间，市场情绪相对平稳。'
    }
  }

  // 市场情绪分析
  if (sentiment.overall === 'up') {
    analysis += '市场整体情绪偏多，多数品种上涨。'
  }
  else if (sentiment.overall === 'down') {
    analysis += '市场整体情绪偏空，多数品种下跌。'
  }
  else {
    analysis += '市场情绪分化，涨跌互现。'
  }

  return analysis
}

// 生成投资建议
function generateSuggestions() {
  const tech = technicalAnalysis.value
  const sentiment = marketSentiment.value
  const suggestions = []

  if (tech.trend === 'up' && sentiment.overall === 'up') {
    suggestions.push({
      type: 'up',
      text: '技术面和市场情绪均偏多，可考虑适量做多，但需注意风险控制。',
    })
  }
  else if (tech.trend === 'down' && sentiment.overall === 'down') {
    suggestions.push({
      type: 'down',
      text: '技术面和市场情绪均偏空，建议谨慎操作，可考虑减仓或观望。',
    })
  }
  else {
    suggestions.push({
      type: 'neutral',
      text: '技术面和市场情绪存在分歧，建议保持中性仓位，等待明确信号。',
    })
  }

  // RSI建议
  const rsiValue = Number.parseFloat(tech.rsi)
  if (!Number.isNaN(rsiValue)) {
    if (rsiValue > 70) {
      suggestions.push({
        type: 'down',
        text: 'RSI超买，建议逢高减仓，控制风险。',
      })
    }
    else if (rsiValue < 30) {
      suggestions.push({
        type: 'up',
        text: 'RSI超卖，可关注低位买入机会。',
      })
    }
  }

  return suggestions
}

// 风险提示
const riskWarning = computed(() => {
  const warnings = []

  if (props.chartData && props.chartData.length > 0) {
    const latest = props.chartData[props.chartData.length - 1]
    const volatility = Math.abs(latest.high - latest.low) / latest.close

    if (volatility > 0.03) {
      warnings.push('当前市场波动较大，请注意风险控制。')
    }
  }

  const rsiValue = Number.parseFloat(technicalAnalysis.value.rsi)
  if (!Number.isNaN(rsiValue) && (rsiValue > 80 || rsiValue < 20)) {
    warnings.push('技术指标显示极端状态，市场可能面临反转风险。')
  }

  warnings.push('投资有风险，入市需谨慎。以上分析仅供参考，不构成投资建议。')

  return warnings
})

// 加载AI分析数据
async function loadAIAnalysis() {
  loading.value = true
  try {
    const response = await goldApi.getAIAnalysis()
    analysisData.value = response
  }
  catch (error) {
    console.error('加载AI分析失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 监听数据变化
watch(() => [props.chartData, props.marketData], () => {
  // 数据变化时可以重新加载AI分析
}, { deep: true })

onMounted(() => {
  loadAIAnalysis()
})
</script>

<template>
  <view class="ai-analysis">
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner" />
      <text class="loading-text">
        AI分析中...
      </text>
    </view>

    <view v-else class="analysis-content">
      <!-- 技术指标 -->
      <view class="analysis-section">
        <text class="section-title">
          技术指标
        </text>
        <view class="indicators-grid">
          <view class="indicator-item">
            <text class="indicator-name">
              MA5
            </text>
            <text class="indicator-value">
              {{ technicalAnalysis.ma5 }}
            </text>
          </view>
          <view class="indicator-item">
            <text class="indicator-name">
              MA10
            </text>
            <text class="indicator-value">
              {{ technicalAnalysis.ma10 }}
            </text>
          </view>
          <view class="indicator-item">
            <text class="indicator-name">
              MA20
            </text>
            <text class="indicator-value">
              {{ technicalAnalysis.ma20 }}
            </text>
          </view>
          <view class="indicator-item">
            <text class="indicator-name">
              RSI
            </text>
            <text class="indicator-value" :class="technicalAnalysis.trend">
              {{ technicalAnalysis.rsi }}
            </text>
          </view>
        </view>
      </view>

      <!-- 市场分析 -->
      <view class="analysis-section">
        <text class="section-title">
          市场分析
        </text>
        <text class="analysis-text">
          {{ generateAnalysisText() }}
        </text>
      </view>

      <!-- 投资建议 -->
      <view class="analysis-section">
        <text class="section-title">
          投资建议
        </text>
        <view class="suggestions-list">
          <view v-for="(suggestion, index) in generateSuggestions()" :key="index" class="suggestion-item">
            <view class="suggestion-type" :class="suggestion.type">
              {{ suggestion.type === 'up' ? '看多' : suggestion.type === 'down' ? '看空' : '中性' }}
            </view>
            <text class="suggestion-text">
              {{ suggestion.text }}
            </text>
          </view>
        </view>
      </view>

      <!-- 风险提示 -->
      <view class="analysis-section">
        <text class="section-title">
          风险提示
        </text>
        <view class="risk-warnings">
          <text v-for="(warning, index) in riskWarning" :key="index" class="risk-text">
            {{ warning }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.ai-analysis {
  width: 100%;
  min-height: 400rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(244, 162, 97, 0.3);
  border-top: 4rpx solid #f4a261;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  color: #8d5524;
  font-size: 24rpx;
  font-weight: 500;
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.analysis-section {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.88) 100%);
  border-radius: 20rpx;
  padding: 28rpx;
  border: 2rpx solid rgba(244, 162, 97, 0.25);
  box-shadow:
    0 6rpx 24rpx rgba(231, 111, 81, 0.1),
    0 2rpx 8rpx rgba(244, 162, 97, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15px);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #8d5524 0%, #a0522d 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #8d5524;
  margin-bottom: 20rpx;
  display: block;
}

.indicators-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 18rpx;
}

.indicator-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18rpx 24rpx;
  background: linear-gradient(135deg, rgba(244, 162, 97, 0.08) 0%, rgba(233, 196, 106, 0.05) 100%);
  border-radius: 16rpx;
  border: 1rpx solid rgba(244, 162, 97, 0.2);
}

.indicator-name {
  font-size: 22rpx;
  color: #8d5524;
  font-weight: 500;
}

.indicator-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #a0522d;
}

.analysis-text {
  font-size: 26rpx;
  color: #8d5524;
  line-height: 1.8;
  font-weight: 400;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 18rpx;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 18rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(244, 162, 97, 0.06) 0%, rgba(233, 196, 106, 0.04) 100%);
  border-radius: 16rpx;
  border: 1rpx solid rgba(244, 162, 97, 0.15);
}

.suggestion-type {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 600;
  white-space: nowrap;
  border: 1rpx solid rgba(244, 162, 97, 0.3);
}

.suggestion-text {
  font-size: 24rpx;
  color: #8d5524;
  line-height: 1.7;
  flex: 1;
  font-weight: 400;
}

.risk-warnings {
  display: flex;
  flex-direction: column;
  gap: 18rpx;
}

.risk-text {
  font-size: 24rpx;
  color: #e76f51;
  line-height: 1.8;
  background: linear-gradient(135deg, rgba(231, 111, 81, 0.1) 0%, rgba(244, 162, 97, 0.08) 100%);
  padding: 20rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid #e76f51;
  border: 1rpx solid rgba(231, 111, 81, 0.2);
  font-weight: 500;
}

/* 状态颜色 - 使用温暖色调 */
.up {
  color: #d2691e !important; /* 橙红色表示上涨 */
}

.down {
  color: #cd853f !important; /* 秘鲁色表示下跌 */
}

.neutral {
  color: #daa520 !important; /* 金黄色表示持平 */
}

.suggestion-type.up {
  background: linear-gradient(135deg, rgba(210, 105, 30, 0.15) 0%, rgba(244, 162, 97, 0.1) 100%);
  color: #d2691e;
  border-color: rgba(210, 105, 30, 0.3);
}

.suggestion-type.down {
  background: linear-gradient(135deg, rgba(205, 133, 63, 0.15) 0%, rgba(244, 162, 97, 0.1) 100%);
  color: #cd853f;
  border-color: rgba(205, 133, 63, 0.3);
}

.suggestion-type.neutral {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.15) 0%, rgba(233, 196, 106, 0.1) 100%);
  color: #daa520;
  border-color: rgba(218, 165, 32, 0.3);
}
</style>
